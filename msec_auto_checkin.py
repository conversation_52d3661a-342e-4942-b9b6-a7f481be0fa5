#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC自动签到脚本
功能：模拟用户登录绿盟科技MSEC平台并执行自动签到
作者：AI Assistant
版本：1.0.0
"""

import requests
import json
import base64
import time
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional, <PERSON>ple
import os
import sys
import sqlite3
import hashlib
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class MSECCrypto:
    """加密解密工具类"""

    @staticmethod
    def generate_key_from_password(password: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """
        基于密码生成加密密钥

        Args:
            password: 用户密码
            salt: 盐值，如果为None则生成新的

        Returns:
            (key, salt) 元组
        """
        if salt is None:
            salt = os.urandom(16)

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt

    @staticmethod
    def encrypt_data(data: str, password: str) -> str:
        """
        加密数据

        Args:
            data: 要加密的数据
            password: 用户密码

        Returns:
            加密后的数据（base64编码）
        """
        key, salt = MSECCrypto.generate_key_from_password(password)
        fernet = Fernet(key)
        encrypted_data = fernet.encrypt(data.encode())

        # 将salt和加密数据组合
        combined = salt + encrypted_data
        return base64.b64encode(combined).decode()

    @staticmethod
    def decrypt_data(encrypted_data: str, password: str) -> Optional[str]:
        """
        解密数据

        Args:
            encrypted_data: 加密的数据（base64编码）
            password: 用户密码

        Returns:
            解密后的数据，失败返回None
        """
        try:
            combined = base64.b64decode(encrypted_data.encode())
            salt = combined[:16]  # 前16字节是salt
            encrypted_content = combined[16:]  # 剩余部分是加密数据

            key, _ = MSECCrypto.generate_key_from_password(password, salt)
            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(encrypted_content)

            return decrypted_data.decode()
        except Exception:
            return None


class MSECDatabase:
    """数据库管理类"""

    def __init__(self, db_path: str = "msec_data.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 创建用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    encrypted_token TEXT,
                    token_exp INTEGER,
                    roles TEXT,
                    checkin_history TEXT,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

    def save_user_data(self, username: str, password: str, token: str = None,
                      token_exp: int = None, roles: Dict = None,
                      checkin_history: Dict = None) -> bool:
        """
        保存用户数据

        Args:
            username: 用户名
            password: 用户密码（用于加密）
            token: JWT token
            token_exp: token过期时间戳
            roles: 角色信息
            checkin_history: 签到历史

        Returns:
            保存是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 加密token
                encrypted_token = None
                if token:
                    try:
                        encrypted_token = MSECCrypto.encrypt_data(token, password)
                        print(f"Token加密成功，长度: {len(encrypted_token)}")
                    except Exception as e:
                        print(f"Token加密失败: {e}")
                        return False

                # 序列化JSON数据
                roles_json = json.dumps(roles) if roles else None
                history_json = json.dumps(checkin_history) if checkin_history else None

                # 插入或更新用户数据
                cursor.execute('''
                    INSERT OR REPLACE INTO users
                    (username, encrypted_token, token_exp, roles, checkin_history, last_updated)
                    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (username, encrypted_token, token_exp, roles_json, history_json))

                conn.commit()
                print(f"用户数据保存成功: {username}")
                return True

        except Exception as e:
            print(f"保存用户数据失败: {e}")
            return False

    def get_user_data(self, username: str, password: str) -> Optional[Dict]:
        """
        获取用户数据

        Args:
            username: 用户名
            password: 用户密码（用于解密）

        Returns:
            用户数据字典或None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT encrypted_token, token_exp, roles, checkin_history, last_updated
                    FROM users WHERE username = ?
                ''', (username,))

                result = cursor.fetchone()
                if not result:
                    print(f"未找到用户数据: {username}")
                    return None

                encrypted_token, token_exp, roles_json, history_json, last_updated = result
                print(f"数据库查询结果: encrypted_token={'存在' if encrypted_token else '不存在'}, token_exp={token_exp}")

                # 解密token
                token = None
                if encrypted_token:
                    try:
                        token = MSECCrypto.decrypt_data(encrypted_token, password)
                        if token is None:
                            print("Token解密失败：密码错误或数据损坏")
                            return None
                        else:
                            print(f"Token解密成功，长度: {len(token)}")
                    except Exception as e:
                        print(f"Token解密异常: {e}")
                        return None

                # 解析JSON数据
                roles = json.loads(roles_json) if roles_json else None
                checkin_history = json.loads(history_json) if history_json else None

                return {
                    'token': token,
                    'token_exp': token_exp,
                    'roles': roles,
                    'checkin_history': checkin_history,
                    'last_updated': last_updated
                }

        except Exception as e:
            print(f"获取用户数据失败: {e}")
            return None

    def is_token_valid(self, username: str, password: str) -> bool:
        """
        检查token是否有效（未过期）

        Args:
            username: 用户名
            password: 用户密码

        Returns:
            token是否有效
        """
        user_data = self.get_user_data(username, password)
        if not user_data or not user_data.get('token') or not user_data.get('token_exp'):
            return False

        # 检查是否过期（提前5分钟判断过期）
        current_time = int(time.time())
        token_exp = user_data['token_exp']

        return current_time < (token_exp - 300)  # 提前5分钟


class MSECConfig:
    """MSEC配置类"""

    # 基础配置
    BASE_URL = "https://msec.nsfocus.com"
    LOGIN_URL = "https://msec.nsfocus.com/auth/login"

    # API端点
    API_ENDPOINTS = {
        'captcha': '/backend_api/account/captcha',
        'login': '/backend_api/account/login',
        'user_info': '/backend_api/account/info',
        'points': '/backend_api/point/common/get',
        'roles': '/backend_api/rbac/role/self/list',
        'checkin_history': '/backend_api/checkin/history',
        'checkin': '/backend_api/checkin/checkin'
    }

    # 请求头配置（仅包含必要的请求头）
    HEADERS = {
        'Host': 'msec.nsfocus.com',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Origin': 'https://msec.nsfocus.com',
        'Referer': 'https://msec.nsfocus.com/auth/login',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    # 超时配置
    REQUEST_TIMEOUT = 30
    RETRY_COUNT = 3
    RETRY_DELAY = 2


class MSECLogger:
    """日志管理类"""

    def __init__(self, log_level=logging.INFO):
        self.logger = logging.getLogger('MSEC_AutoCheckin')
        self.logger.setLevel(log_level)

        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)

        # 添加处理器
        if not self.logger.handlers:
            self.logger.addHandler(console_handler)

    def info(self, message: str):
        self.logger.info(message)

    def error(self, message: str):
        self.logger.error(message)

    def warning(self, message: str):
        self.logger.warning(message)

    def debug(self, message: str):
        self.logger.debug(message)


class MSECClient:
    """MSEC客户端主类"""

    def __init__(self, username: str, password: str):
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.jwt_token = None
        self.user_info = None
        self.logger = MSECLogger()
        self.db = MSECDatabase()

        # 设置基础请求头
        self.session.headers.update(MSECConfig.HEADERS)

        # 尝试从数据库加载token
        self._load_token_from_db()

    def _load_token_from_db(self):
        """从数据库加载token"""
        try:
            if self.db.is_token_valid(self.username, self.password):
                user_data = self.db.get_user_data(self.username, self.password)
                if user_data and user_data.get('token'):
                    self.jwt_token = user_data['token']
                    self.session.headers['Authorization'] = self.jwt_token
                    self.logger.info("从数据库加载有效token成功")
                    return True
        except Exception as e:
            self.logger.debug(f"从数据库加载token失败: {e}")
        return False

    def _save_token_to_db(self, token: str):
        """保存token到数据库"""
        try:
            # 解析JWT token获取过期时间
            import jwt
            decoded = jwt.decode(token, options={"verify_signature": False})
            token_exp = decoded.get('exp', 0)

            success = self.db.save_user_data(
                username=self.username,
                password=self.password,
                token=token,
                token_exp=token_exp
            )
            if success:
                self.logger.info("Token保存到数据库成功")
            else:
                self.logger.error("Token保存到数据库失败")
        except Exception as e:
            # 如果无法解析JWT，使用默认过期时间（24小时后）
            token_exp = int(time.time()) + 24 * 3600
            success = self.db.save_user_data(
                username=self.username,
                password=self.password,
                token=token,
                token_exp=token_exp
            )
            if success:
                self.logger.warning(f"无法解析JWT过期时间，使用默认值: {e}")
            else:
                self.logger.error(f"Token保存到数据库失败: {e}")

    def _save_user_data_to_db(self, roles: Dict = None, checkin_history: Dict = None):
        """保存用户数据到数据库"""
        self.db.save_user_data(
            username=self.username,
            password=self.password,
            roles=roles,
            checkin_history=checkin_history
        )

    def is_logged_in(self) -> bool:
        """检查是否已登录（token有效）"""
        return self.jwt_token is not None and self.db.is_token_valid(self.username, self.password)

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                     retry_count: int = MSECConfig.RETRY_COUNT) -> Optional[requests.Response]:
        """
        发送HTTP请求的通用方法

        Args:
            method: HTTP方法 (GET, POST等)
            endpoint: API端点
            data: 请求数据
            retry_count: 重试次数

        Returns:
            Response对象或None
        """
        url = MSECConfig.BASE_URL + endpoint

        for attempt in range(retry_count + 1):
            try:
                self.logger.info(f"发送{method}请求到: {endpoint} (尝试 {attempt + 1}/{retry_count + 1})")

                if method.upper() == 'POST':
                    response = self.session.post(
                        url,
                        json=data or {},
                        timeout=MSECConfig.REQUEST_TIMEOUT
                    )
                else:
                    response = self.session.get(
                        url,
                        timeout=MSECConfig.REQUEST_TIMEOUT
                    )

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt < retry_count:
                    time.sleep(MSECConfig.RETRY_DELAY)
                else:
                    self.logger.error(f"请求最终失败: {endpoint}")
                    return None

    def get_captcha(self) -> Tuple[Optional[str], Optional[str]]:
        """
        获取验证码

        Returns:
            (captcha_id, captcha_image_base64) 或 (None, None)
        """
        self.logger.info("正在获取验证码...")

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['captcha'])
        if not response:
            self.logger.error("获取验证码失败：请求失败")
            return None, None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"验证码响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取验证码失败：服务器返回状态 {response_data.get('status')}")
                return None, None

            # 提取验证码信息
            data = response_data.get('data', {})
            captcha_id = data.get('id')
            captcha_image = data.get('captcha')

            if not captcha_id or not captcha_image:
                self.logger.error("获取验证码失败：响应数据不完整")
                return None, None

            self.logger.info(f"验证码获取成功，ID: {captcha_id}")

            # 打印验证码base64数据到控制台
            print("\n" + "="*80)
            print("验证码图片 (Base64编码):")
            print("="*80)
            print(captcha_image)
            print("="*80)
            print(f"验证码ID: {captcha_id}")
            print("="*80 + "\n")

            return captcha_id, captcha_image

        except json.JSONDecodeError as e:
            self.logger.error(f"获取验证码失败：JSON解析错误 - {str(e)}")
            return None, None
        except Exception as e:
            self.logger.error(f"获取验证码失败：未知错误 - {str(e)}")
            return None, None

    def login(self, captcha_id: str, captcha_answer: str) -> bool:
        """
        用户登录

        Args:
            captcha_id: 验证码ID
            captcha_answer: 验证码答案

        Returns:
            登录是否成功
        """
        self.logger.info("正在尝试登录...")

        # 构建登录请求数据
        login_data = {
            "captcha_answer": captcha_answer,
            "captcha_id": captcha_id,
            "password": self.password,
            "username": self.username
        }

        self.logger.debug(f"登录请求数据: {login_data}")

        # 临时更新Referer为登录页面
        original_referer = self.session.headers.get('Referer')
        self.session.headers['Referer'] = 'https://msec.nsfocus.com/auth/login'

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['login'], login_data)

        # 恢复原始Referer
        if original_referer:
            self.session.headers['Referer'] = original_referer

        if not response:
            self.logger.error("登录失败：请求失败")
            return False

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"登录响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"登录失败：服务器返回状态 {response_data.get('status')}")
                return False

            # 提取JWT token
            data = response_data.get('data', {})
            jwt_token = data.get('token')

            if not jwt_token:
                self.logger.error("登录失败：未获取到JWT token")
                return False

            # 保存JWT token并更新请求头
            self.jwt_token = jwt_token
            self.session.headers['Authorization'] = jwt_token

            # 保存token到数据库
            self._save_token_to_db(jwt_token)

            self.logger.info("登录成功！")
            self.logger.info(f"JWT Token: {jwt_token[:50]}...")  # 只显示前50个字符

            return True

        except json.JSONDecodeError as e:
            self.logger.error(f"登录失败：JSON解析错误 - {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"登录失败：未知错误 - {str(e)}")
            return False

    def get_user_info(self) -> Optional[Dict]:
        """
        获取用户信息

        Returns:
            用户信息字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取用户信息失败：未登录")
            return None

        self.logger.info("正在获取用户信息...")

        # 更新Referer为主页
        self.session.headers['Referer'] = 'https://msec.nsfocus.com/'

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['user_info'])
        if not response:
            self.logger.error("获取用户信息失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"用户信息响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取用户信息失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取用户信息
            data = response_data.get('data', {})
            user_info = data.get('user', {})

            if not user_info:
                self.logger.error("获取用户信息失败：响应数据不完整")
                return None

            # 保存用户信息
            self.user_info = user_info

            self.logger.info("用户信息获取成功！")
            self.logger.info(f"用户名: {user_info.get('username')}")
            self.logger.info(f"昵称: {user_info.get('nickname', '未设置')}")
            self.logger.info(f"邮箱: {user_info.get('email', '未设置')}")
            self.logger.info(f"手机: {user_info.get('phone', '未设置')}")

            return user_info

        except json.JSONDecodeError as e:
            self.logger.error(f"获取用户信息失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取用户信息失败：未知错误 - {str(e)}")
            return None

    def get_points(self) -> Optional[Dict]:
        """
        获取用户积分信息

        Returns:
            积分信息字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取积分信息失败：未登录")
            return None

        self.logger.info("正在获取积分信息...")

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['points'])
        if not response:
            self.logger.error("获取积分信息失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"积分信息响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取积分信息失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取积分信息
            data = response_data.get('data', {})

            if 'accrued' not in data or 'total' not in data:
                self.logger.error("获取积分信息失败：响应数据不完整")
                return None

            self.logger.info("积分信息获取成功！")
            self.logger.info(f"当前积分: {data.get('accrued')}")
            self.logger.info(f"总积分: {data.get('total')}")

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"获取积分信息失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取积分信息失败：未知错误 - {str(e)}")
            return None

    def get_roles(self) -> Optional[Dict]:
        """
        获取用户角色信息

        Returns:
            角色信息字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取角色信息失败：未登录")
            return None

        self.logger.info("正在获取角色信息...")

        # 构建请求数据
        role_data = {
            "limit": 100,
            "offset": 0
        }

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['roles'], role_data)
        if not response:
            self.logger.error("获取角色信息失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"角色信息响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取角色信息失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取角色信息
            data = response_data.get('data', {})
            role_list = data.get('list', [])

            if not role_list:
                self.logger.warning("未获取到角色信息")
                return data

            self.logger.info("角色信息获取成功！")
            for role in role_list:
                role_name = role.get('role_name', '未知角色')
                expire_time = role.get('expire_time', 0)
                if expire_time == 0:
                    expire_info = "永久有效"
                else:
                    expire_info = f"过期时间: {expire_time}"
                self.logger.info(f"角色: {role_name} ({expire_info})")

            # 保存角色信息到数据库
            self._save_user_data_to_db(roles=data)

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"获取角色信息失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取角色信息失败：未知错误 - {str(e)}")
            return None

    def get_checkin_history(self, start_date: str, days: int = 5) -> Optional[Dict]:
        """
        获取签到历史

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            days: 查询天数

        Returns:
            签到历史字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取签到历史失败：未登录")
            return None

        self.logger.info(f"正在获取签到历史 (从 {start_date} 开始，{days} 天)...")

        # 构建请求数据
        history_data = {
            "start_date": start_date,
            "days": days
        }

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['checkin_history'], history_data)
        if not response:
            self.logger.error("获取签到历史失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"签到历史响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取签到历史失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取签到历史信息
            data = response_data.get('data', {})
            records_list = data.get('records_list', {})
            checkin_records = records_list.get('list', [])
            total_records = records_list.get('total', 0)

            self.logger.info("签到历史获取成功！")
            self.logger.info(f"查询期间: {data.get('start_date')} 开始，{data.get('days')} 天")
            self.logger.info(f"签到记录总数: {total_records}")

            if checkin_records:
                self.logger.info("签到记录详情:")
                for record in checkin_records:
                    username = record.get('username', '未知')
                    date = record.get('date', '未知')
                    checkin_time = record.get('checkin_time', 0)
                    # 转换时间戳为可读格式
                    if checkin_time:
                        from datetime import datetime
                        readable_time = datetime.fromtimestamp(checkin_time / 1000).strftime('%Y-%m-%d %H:%M:%S')
                        self.logger.info(f"  - {date}: {username} 于 {readable_time} 签到")
                    else:
                        self.logger.info(f"  - {date}: {username} 签到时间未知")
            else:
                self.logger.info("查询期间内无签到记录")

            # 保存签到历史到数据库
            self._save_user_data_to_db(checkin_history=data)

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"获取签到历史失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取签到历史失败：未知错误 - {str(e)}")
            return None

    def checkin(self) -> bool:
        """
        执行签到

        Returns:
            签到是否成功
        """
        if not self.jwt_token:
            self.logger.error("签到失败：未登录")
            return False

        self.logger.info("正在执行签到...")

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['checkin'])
        if not response:
            self.logger.error("签到失败：请求失败")
            return False

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"签到响应数据: {response_data}")

            # 检查响应状态
            status = response_data.get('status')
            if status == 200:
                self.logger.info("✅ 签到成功！")
                # 获取当前时间作为签到时间
                from datetime import datetime
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.logger.info(f"签到时间: {current_time}")
                return True
            elif status == 400:
                self.logger.warning("⚠️ 今日已签到，无需重复签到")
                return True  # 已签到也算成功
            else:
                self.logger.error(f"签到失败：服务器返回状态 {status}")
                return False

        except json.JSONDecodeError as e:
            self.logger.error(f"签到失败：JSON解析错误 - {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"签到失败：未知错误 - {str(e)}")
            return False

    def auto_checkin_flow(self) -> bool:
        """
        自动签到完整流程

        Returns:
            整个流程是否成功
        """
        self.logger.info("开始执行自动签到流程...")

        try:
            # 检查是否已有有效token
            if self.is_logged_in():
                self.logger.info("检测到有效token，跳过登录步骤")
            else:
                # 步骤1: 获取验证码
                self.logger.info("步骤1: 获取验证码")
                captcha_id, captcha_image = self.get_captcha()

                if not captcha_id or not captcha_image:
                    self.logger.error("自动签到流程失败：验证码获取失败")
                    return False

                # 用户输入验证码答案
                captcha_answer = input("\n请查看上方验证码图片，输入验证码答案: ").strip()

                if not captcha_answer:
                    self.logger.error("自动签到流程失败：验证码答案不能为空")
                    return False

                # 步骤2: 用户登录
                self.logger.info("步骤2: 用户登录")
                login_success = self.login(captcha_id, captcha_answer)

                if not login_success:
                    self.logger.error("自动签到流程失败：登录失败")
                    return False

            # 步骤3: 获取用户信息
            self.logger.info("步骤3: 获取用户信息")
            user_info = self.get_user_info()

            if not user_info:
                self.logger.warning("获取用户信息失败，但继续执行签到流程")

            # 步骤4: 获取积分信息
            self.logger.info("步骤4: 获取积分信息")
            points_info = self.get_points()

            if not points_info:
                self.logger.warning("获取积分信息失败，但继续执行签到流程")

            # 步骤5: 获取角色信息
            self.logger.info("步骤5: 获取角色信息")
            roles_info = self.get_roles()

            if not roles_info:
                self.logger.warning("获取角色信息失败，但继续执行签到流程")

            # 步骤6: 获取签到历史（最近5天）
            self.logger.info("步骤6: 获取签到历史")
            from datetime import datetime, timedelta
            start_date = (datetime.now() - timedelta(days=4)).strftime('%Y-%m-%d')
            history_before = self.get_checkin_history(start_date, 5)

            if not history_before:
                self.logger.warning("获取签到历史失败，但继续执行签到流程")

            # 步骤7: 执行签到
            self.logger.info("步骤7: 执行签到")
            checkin_success = self.checkin()

            if not checkin_success:
                self.logger.error("自动签到流程失败：签到操作失败")
                return False

            # 步骤8: 再次获取签到历史以确认签到成功
            self.logger.info("步骤8: 确认签到结果")
            time.sleep(2)  # 等待2秒确保数据更新
            history_after = self.get_checkin_history(start_date, 5)

            if history_after:
                records_after = history_after.get('records_list', {}).get('list', [])
                records_before = history_before.get('records_list', {}).get('list', []) if history_before else []

                if len(records_after) > len(records_before):
                    self.logger.info("✅ 签到确认成功：签到记录已更新")
                else:
                    self.logger.warning("⚠️ 签到状态未确定：签到记录未发现变化")

            self.logger.info("🎉 自动签到流程执行完成！")
            return True

        except KeyboardInterrupt:
            self.logger.info("用户中断了自动签到流程")
            return False
        except Exception as e:
            self.logger.error(f"自动签到流程失败：未知错误 - {str(e)}")
            return False


def test_captcha():
    """测试验证码获取功能"""
    print("MSEC验证码获取测试")
    print("=" * 50)

    # 创建一个临时客户端用于测试
    client = MSECClient("test", "test")

    # 测试获取验证码
    captcha_id, captcha_image = client.get_captcha()

    if captcha_id and captcha_image:
        print("✅ 验证码获取成功！")
        print(f"验证码ID: {captcha_id}")
        print(f"验证码图片长度: {len(captcha_image)} 字符")
    else:
        print("❌ 验证码获取失败！")


def test_login():
    """测试登录功能"""
    print("MSEC登录功能测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 获取验证码
    print("\n步骤1: 获取验证码")
    captcha_id, captcha_image = client.get_captcha()

    if not captcha_id or not captcha_image:
        print("❌ 验证码获取失败！")
        return

    # 用户输入验证码答案
    captcha_answer = input("\n请查看上方验证码图片，输入验证码答案: ").strip()

    if not captcha_answer:
        print("错误：验证码答案不能为空")
        return

    # 尝试登录
    print("\n步骤2: 尝试登录")
    success = client.login(captcha_id, captcha_answer)

    if success:
        print("✅ 登录成功！")
        print(f"JWT Token: {client.jwt_token[:50]}...")
    else:
        print("❌ 登录失败！")


def test_user_info():
    """测试用户信息获取功能"""
    print("MSEC用户信息获取测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 获取验证码并登录
    print("\n步骤1: 获取验证码")
    captcha_id, captcha_image = client.get_captcha()

    if not captcha_id or not captcha_image:
        print("❌ 验证码获取失败！")
        return

    captcha_answer = input("\n请查看上方验证码图片，输入验证码答案: ").strip()

    if not captcha_answer:
        print("错误：验证码答案不能为空")
        return

    print("\n步骤2: 尝试登录")
    success = client.login(captcha_id, captcha_answer)

    if not success:
        print("❌ 登录失败！")
        return

    print("✅ 登录成功！")

    # 获取用户信息
    print("\n步骤3: 获取用户信息")
    user_info = client.get_user_info()

    # 获取积分信息
    print("\n步骤4: 获取积分信息")
    points_info = client.get_points()

    # 获取角色信息
    print("\n步骤5: 获取角色信息")
    roles_info = client.get_roles()

    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"用户信息: {'✅ 成功' if user_info else '❌ 失败'}")
    print(f"积分信息: {'✅ 成功' if points_info else '❌ 失败'}")
    print(f"角色信息: {'✅ 成功' if roles_info else '❌ 失败'}")
    print("="*50)


def test_checkin():
    """测试签到功能"""
    print("MSEC签到功能测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 获取验证码并登录
    print("\n步骤1: 获取验证码")
    captcha_id, captcha_image = client.get_captcha()

    if not captcha_id or not captcha_image:
        print("❌ 验证码获取失败！")
        return

    captcha_answer = input("\n请查看上方验证码图片，输入验证码答案: ").strip()

    if not captcha_answer:
        print("错误：验证码答案不能为空")
        return

    print("\n步骤2: 尝试登录")
    success = client.login(captcha_id, captcha_answer)

    if not success:
        print("❌ 登录失败！")
        return

    print("✅ 登录成功！")

    # 获取签到历史
    print("\n步骤3: 获取签到历史")
    from datetime import datetime, timedelta
    start_date = (datetime.now() - timedelta(days=4)).strftime('%Y-%m-%d')
    history_before = client.get_checkin_history(start_date, 5)

    # 执行签到
    print("\n步骤4: 执行签到")
    checkin_success = client.checkin()

    # 再次获取签到历史
    print("\n步骤5: 确认签到结果")
    if checkin_success:
        time.sleep(2)  # 等待数据更新
        history_after = client.get_checkin_history(start_date, 5)

    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"签到历史获取: {'✅ 成功' if history_before else '❌ 失败'}")
    print(f"签到操作: {'✅ 成功' if checkin_success else '❌ 失败'}")
    print("="*50)


def test_database():
    """测试数据库功能"""
    print("MSEC数据库功能测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    print("\n步骤1: 测试数据库初始化")
    print(f"数据库文件: {client.db.db_path}")

    print("\n步骤2: 测试token持久化")
    if client.is_logged_in():
        print("✅ 检测到有效token，无需重新登录")
        print(f"Token: {client.jwt_token[:50]}...")
    else:
        print("❌ 未检测到有效token，需要重新登录")

        # 获取验证码并登录
        print("\n获取验证码...")
        captcha_id, captcha_image = client.get_captcha()

        if not captcha_id or not captcha_image:
            print("❌ 验证码获取失败！")
            return

        captcha_answer = input("\n请查看上方验证码图片，输入验证码答案: ").strip()

        if not captcha_answer:
            print("错误：验证码答案不能为空")
            return

        print("\n尝试登录...")
        success = client.login(captcha_id, captcha_answer)

        if success:
            print("✅ 登录成功，token已保存到数据库")
        else:
            print("❌ 登录失败！")
            return

    print("\n步骤3: 测试数据存储")
    # 获取并保存用户数据
    user_info = client.get_user_info()
    points_info = client.get_points()
    roles_info = client.get_roles()

    from datetime import datetime, timedelta
    start_date = (datetime.now() - timedelta(days=4)).strftime('%Y-%m-%d')
    history_info = client.get_checkin_history(start_date, 5)

    print("\n步骤4: 验证数据库存储")
    # 直接检查数据库中的数据
    user_data = client.db.get_user_data(username, password)
    if user_data:
        print("✅ 用户数据读取成功")
        print(f"Token存在: {'是' if user_data.get('token') else '否'}")
        if user_data.get('token_exp'):
            print(f"Token过期时间: {datetime.fromtimestamp(user_data['token_exp']).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"角色信息: {'已存储' if user_data.get('roles') else '未存储'}")
        print(f"签到历史: {'已存储' if user_data.get('checkin_history') else '未存储'}")

        # 检查token是否有效
        is_valid = client.db.is_token_valid(username, password)
        print(f"Token有效性: {'有效' if is_valid else '无效/过期'}")
    else:
        print("❌ 用户数据读取失败")

    # 重新创建客户端，测试数据加载
    print("\n创建新客户端实例测试...")
    client2 = MSECClient(username, password)

    if client2.is_logged_in():
        print("✅ 新客户端token加载成功")
    else:
        print("❌ 新客户端token加载失败")

    # 总结
    print("\n" + "="*50)
    print("数据库功能测试总结:")
    print(f"数据库初始化: ✅ 成功")
    print(f"Token持久化: {'✅ 成功' if client.is_logged_in() else '❌ 失败'}")
    print(f"数据存储: {'✅ 成功' if user_info and roles_info else '❌ 失败'}")
    print(f"数据加载: {'✅ 成功' if client2.is_logged_in() else '❌ 失败'}")
    print("="*50)


def main():
    """主函数"""
    print("MSEC自动签到脚本 v1.0.0 (支持数据库持久化)")
    print("=" * 50)

    # 添加测试选项
    print("请选择操作:")
    print("1. 测试验证码获取")
    print("2. 测试登录功能")
    print("3. 测试用户信息获取")
    print("4. 测试签到功能")
    print("5. 测试数据库功能")
    print("6. 完整自动签到流程")
    choice = input("请输入选择 (1/2/3/4/5/6): ").strip()

    if choice == "1":
        test_captcha()
        return
    elif choice == "2":
        test_login()
        return
    elif choice == "3":
        test_user_info()
        return
    elif choice == "4":
        test_checkin()
        return
    elif choice == "5":
        test_database()
        return
    elif choice == "6":
        # 获取用户凭据
        username = input("请输入用户名: ").strip()
        password = input("请输入密码: ").strip()

        if not username or not password:
            print("错误：用户名和密码不能为空")
            return

        # 创建客户端并执行自动签到
        client = MSECClient(username, password)
        success = client.auto_checkin_flow()

        if success:
            print("\n✅ 自动签到流程执行成功！")
        else:
            print("\n❌ 自动签到流程执行失败！")
    else:
        print("无效的选择！")


if __name__ == "__main__":
    main()